#!/usr/bin/env python3
"""
Debug script to check unit conversions and BlackHawk output
"""

import numpy as np

# Physical constants (CGS)
G = 6.67430e-8      # cm^3/g/s^2
c = 2.99792458e10   # cm/s
hbar = 1.054571817e-27  # erg*s

def schwarzschild_lifetime_analytical(mass):
    """Calculate analytical Schwarzschild black hole lifetime"""
    coefficient = 5120 * np.pi * G**2 / (hbar * c**4)
    lifetime = coefficient * mass**3
    return lifetime

def check_units():
    """Check unit consistency"""
    
    print("Unit Analysis")
    print("=" * 50)
    
    # Check coefficient units
    coeff_units = G**2 / (hbar * c**4)
    print(f"G^2 units: (cm^3/g/s^2)^2 = cm^6/g^2/s^4")
    print(f"hbar units: erg*s = g*cm^2/s^2 * s = g*cm^2/s")
    print(f"c^4 units: (cm/s)^4 = cm^4/s^4")
    print(f"Coefficient units: cm^6/g^2/s^4 / (g*cm^2/s * cm^4/s^4)")
    print(f"                 = cm^6/g^2/s^4 / (g*cm^6/s^5)")
    print(f"                 = s/g^3")
    print(f"With M^3: (s/g^3) * g^3 = s ✓")
    
    # Test for 1kg
    mass_1kg = 1000.0  # grams
    lifetime_1kg = schwarzschild_lifetime_analytical(mass_1kg)
    print(f"\n1kg black hole lifetime: {lifetime_1kg:.2e} seconds")
    
    # Test scaling
    masses = np.array([1.0, 10.0, 100.0, 1000.0])
    print(f"\nLifetime scaling check:")
    print("Mass (g)    Lifetime (s)    Ratio")
    print("-" * 35)
    
    lifetimes = []
    for mass in masses:
        lifetime = schwarzschild_lifetime_analytical(mass)
        lifetimes.append(lifetime)
        if len(lifetimes) > 1:
            ratio = lifetime / lifetimes[0]
            expected_ratio = (mass / masses[0])**3
            print(f"{mass:6.0f}      {lifetime:.2e}    {ratio:.0f} (expected: {expected_ratio:.0f})")
        else:
            print(f"{mass:6.0f}      {lifetime:.2e}    reference")

def check_blackhawk_units():
    """Check BlackHawk output units"""
    
    print(f"\nBlackHawk Unit Analysis")
    print("=" * 50)
    
    # From BlackHawk manual: results are in CGS units
    # Energy in GeV, but spectra should be in CGS
    
    # GeV to erg conversion
    GeV_to_erg = 1.602176634e-3  # This seems wrong!
    print(f"GeV to erg conversion used: {GeV_to_erg}")
    
    # Correct conversion
    GeV_to_erg_correct = 1.602176634e-3  # Actually this is wrong
    # 1 GeV = 1.602176634e-9 erg
    GeV_to_erg_correct = 1.602176634e-9
    print(f"Correct GeV to erg: {GeV_to_erg_correct}")
    
    # This is a factor of 1e6 difference!
    print(f"Error factor: {GeV_to_erg / GeV_to_erg_correct}")

if __name__ == "__main__":
    check_units()
    check_blackhawk_units()
