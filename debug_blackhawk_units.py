#!/usr/bin/env python3
"""
Debug BlackHawk units and calculate correct mass loss rate
"""

import numpy as np
import sys
import os
sys.path.append('.')

def analyze_blackhawk_spectrum():
    """Analyze BlackHawk spectrum output and calculate power correctly"""
    
    # Read the spectrum file
    spectrum_file = "external/blackhawk_v2.3/results/test/instantaneous_primary_spectra.txt"
    
    # Read data (skip header lines)
    data = np.loadtxt(spectrum_file, skiprows=2)
    
    if data.size == 0:
        print("No data found!")
        return
    
    # Extract energy and spectra
    energies = data[:, 0]  # GeV
    
    # Particle columns (from header)
    particles = {
        'photon': 1, 'gluons': 2, 'higgs': 3, 'W+-': 4, 'Z0': 5,
        'neutrinos': 6, 'electron': 7, 'muon': 8, 'tau': 9,
        'up': 10, 'down': 11, 'charm': 12, 'strange': 13, 
        'top': 14, 'bottom': 15, 'graviton': 16, 'DM': 17
    }
    
    print("BlackHawk Spectrum Analysis")
    print("=" * 50)
    print(f"Energy range: {energies[0]:.1f} to {energies[-1]:.1f} GeV")
    print(f"Number of energy points: {len(energies)}")
    
    # Unit conversions
    GeV_to_erg = 1.602176634e-9  # 1 GeV = 1.602e-9 erg
    c_light = 2.99792458e10      # cm/s
    
    print(f"\nUnit conversions:")
    print(f"GeV to erg: {GeV_to_erg}")
    
    # Calculate total power for each particle type
    total_power = 0.0
    
    print(f"\nPower contribution by particle type:")
    print("Particle        dN/dE peak      Power (erg/s)")
    print("-" * 45)
    
    for particle, col_idx in particles.items():
        if col_idx < data.shape[1]:
            spectrum = data[:, col_idx]  # dN/dE in s^-1 GeV^-1
            
            # Calculate power: P = ∫ E * dN/dE dE
            # Convert E from GeV to erg
            energies_erg = energies * GeV_to_erg
            power_spectrum = energies_erg * spectrum  # erg/s per GeV
            
            # Integrate over energy (convert dE from GeV to erg)
            power = np.trapz(power_spectrum, energies * GeV_to_erg)  # erg/s
            
            total_power += power
            
            peak_spectrum = np.max(spectrum) if len(spectrum) > 0 else 0
            print(f"{particle:12s}    {peak_spectrum:10.2e}    {power:10.2e}")
    
    print("-" * 45)
    print(f"{'TOTAL':12s}                        {total_power:10.2e}")
    
    # Calculate mass loss rate
    mass_loss_rate = total_power / c_light**2  # g/s
    
    print(f"\nResults:")
    print(f"Total power: {total_power:.2e} erg/s")
    print(f"Mass loss rate: {mass_loss_rate:.2e} g/s")
    
    # Compare with expected scaling
    # For 1e9g black hole, we expect lifetime ~0.4s
    # So mass loss rate should be roughly M/lifetime = 1e9g / 0.4s = 2.5e9 g/s
    expected_rate = 1e9 / 0.4  # rough estimate
    print(f"Expected order of magnitude: {expected_rate:.2e} g/s")
    print(f"Ratio (calculated/expected): {mass_loss_rate/expected_rate:.2f}")
    
    return total_power, mass_loss_rate

def check_integration_method():
    """Test different integration methods"""
    
    print(f"\nTesting integration methods:")
    
    # Simple test function: E^2 from 1 to 100 GeV
    E = np.logspace(0, 2, 100)  # 1 to 100 GeV
    f = E**2
    
    # Analytical result: ∫E^2 dE from 1 to 100 = (100^3 - 1^3)/3
    analytical = (100**3 - 1**3) / 3
    
    # Numerical integration
    numerical = np.trapz(f, E)
    
    print(f"Analytical integral: {analytical:.2e}")
    print(f"Numerical integral: {numerical:.2e}")
    print(f"Relative error: {abs(numerical-analytical)/analytical:.2%}")

if __name__ == "__main__":
    check_integration_method()
    analyze_blackhawk_spectrum()
