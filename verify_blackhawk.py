#!/usr/bin/env python3
"""
Verify BlackHawk output against theoretical expectations
"""

import numpy as np
import sys
import os
sys.path.append('.')

from src.precompute.blackhawk_wrapper import BlackHawkWrapper
from config.evolution_config import Paths

def theoretical_hawking_power(mass):
    """
    Calculate theoretical Hawking power using Stefan-<PERSON>mann law
    
    P = σ * A * T^4
    where:
    - σ = Stefan-<PERSON> constant
    - A = 4πr_s^2 = surface area
    - T = Hawking temperature
    
    For Schwarzschild black hole:
    - r_s = 2GM/c^2 (Schwarzschild radius)
    - T = ħc^3/(8πkGM) (Hawking temperature)
    """
    
    # Physical constants (CGS)
    G = 6.67430e-8      # cm^3/g/s^2
    c = 2.99792458e10   # cm/s
    hbar = 1.054571817e-27  # erg*s
    k_B = 1.380649e-16  # erg/K
    sigma_SB = 5.670374419e-5  # erg/cm^2/s/K^4 (<PERSON> constant)
    
    # Schwarzschild radius
    r_s = 2 * G * mass / c**2
    
    # Surface area
    A = 4 * np.pi * r_s**2
    
    # Hawking temperature
    T = hbar * c**3 / (8 * np.pi * k_B * G * mass)
    
    # Power (simplified, ignoring greybody factors)
    P = sigma_SB * A * T**4
    
    return P, T, r_s

def test_blackhawk_vs_theory():
    """Test BlackHawk against theoretical predictions"""
    
    print("BlackHawk vs Theory Comparison")
    print("=" * 60)
    
    # Test masses
    test_masses = np.array([1e-3, 1e-2, 1e-1, 1.0, 10.0, 100.0, 1000.0])  # grams
    
    wrapper = BlackHawkWrapper(Paths.BLACKHAWK_DIR)
    
    print("Mass (g)    BlackHawk P (erg/s)    Theory P (erg/s)    Ratio    T (K)")
    print("-" * 80)
    
    for mass in test_masses:
        try:
            # BlackHawk calculation
            result = wrapper.run_blackhawk(mass, 0.0)
            bh_power = result['total_power']
            
            # Theoretical calculation
            theory_power, temperature, r_s = theoretical_hawking_power(mass)
            
            ratio = bh_power / theory_power if theory_power > 0 else 0
            
            print(f"{mass:8.1e}    {bh_power:15.2e}    {theory_power:15.2e}    {ratio:6.1f}    {temperature:.1e}")
            
        except Exception as e:
            print(f"{mass:8.1e}    ERROR: {e}")

def check_mass_scaling():
    """Check if mass loss rate scales as 1/M^2"""
    
    print(f"\nMass Loss Rate Scaling Check")
    print("=" * 60)
    
    wrapper = BlackHawkWrapper(Paths.BLACKHAWK_DIR)
    
    # Test masses with exact factors of 10
    masses = np.array([1.0, 10.0, 100.0])  # grams
    
    print("Mass (g)    dM/dt (g/s)    Expected ratio    Actual ratio")
    print("-" * 60)
    
    rates = []
    for i, mass in enumerate(masses):
        try:
            result = wrapper.run_blackhawk(mass, 0.0)
            rate = result['mass_loss_rate']
            rates.append(rate)
            
            if i == 0:
                print(f"{mass:6.1f}      {rate:.2e}      reference         reference")
            else:
                expected_ratio = (masses[0] / mass)**2
                actual_ratio = rates[0] / rate
                print(f"{mass:6.1f}      {rate:.2e}      {expected_ratio:8.0f}          {actual_ratio:8.1f}")
                
        except Exception as e:
            print(f"{mass:6.1f}      ERROR: {e}")

if __name__ == "__main__":
    test_blackhawk_vs_theory()
    check_mass_scaling()
