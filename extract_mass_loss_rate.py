#!/usr/bin/env python3
"""
Extract mass loss rate from BlackHawk time evolution data
and compare with instantaneous spectrum calculation
"""

import numpy as np
import matplotlib.pyplot as plt

def extract_mass_loss_from_evolution():
    """Extract mass loss rate from time evolution data"""
    
    # Read evolution data
    evolution_file = "external/blackhawk_v2.3/results/test/life_evolutions.txt"
    
    # Read data (skip header lines)
    data = np.loadtxt(evolution_file, skiprows=4)
    
    times = data[:, 0]  # seconds
    masses = data[:, 1]  # grams
    spins = data[:, 2]   # dimensionless
    
    print("Mass Loss Rate from Time Evolution")
    print("=" * 50)
    print(f"Initial mass: {masses[0]:.2e} g")
    print(f"Final mass: {masses[-1]:.2e} g")
    print(f"Total lifetime: {times[-1]:.2e} s")
    print(f"Number of time steps: {len(times)}")
    
    # Calculate mass loss rate at different points
    # Use central differences for better accuracy
    dt = np.diff(times)
    dm = np.diff(masses)

    # Avoid division by zero
    dt_safe = np.where(dt == 0, np.inf, dt)
    dmdt = dm / dt_safe  # g/s (negative values)
    
    # Time points for dmdt (midpoints)
    t_mid = (times[1:] + times[:-1]) / 2
    m_mid = (masses[1:] + masses[:-1]) / 2
    
    # Take absolute value since we want positive mass loss rate
    mass_loss_rate = -dmdt
    
    print(f"\nMass loss rate analysis:")
    print("Time (s)        Mass (g)        dM/dt (g/s)")
    print("-" * 50)
    
    # Show rates at different stages
    indices = [0, len(t_mid)//4, len(t_mid)//2, 3*len(t_mid)//4, -1]
    for i in indices:
        if i < len(t_mid):
            print(f"{t_mid[i]:10.2e}    {m_mid[i]:10.2e}    {mass_loss_rate[i]:10.2e}")
    
    # Check scaling: dM/dt should scale as 1/M^2
    print(f"\nScaling check (dM/dt should ∝ 1/M²):")
    print("Mass ratio    Rate ratio    Expected ratio")
    print("-" * 45)
    
    for i in [len(t_mid)//4, len(t_mid)//2, 3*len(t_mid)//4]:
        if i < len(t_mid):
            mass_ratio = m_mid[0] / m_mid[i]
            rate_ratio = mass_loss_rate[i] / mass_loss_rate[0]
            expected_ratio = mass_ratio**2
            print(f"{mass_ratio:8.2f}      {rate_ratio:8.2f}      {expected_ratio:8.2f}")
    
    return t_mid, m_mid, mass_loss_rate

def analyze_instantaneous_spectrum():
    """Analyze instantaneous spectrum and calculate power correctly"""
    
    spectrum_file = "external/blackhawk_v2.3/results/test/instantaneous_primary_spectra.txt"
    
    # Read data
    data = np.loadtxt(spectrum_file, skiprows=2)
    energies = data[:, 0]  # GeV
    
    # Sum all particle contributions
    total_spectrum = np.sum(data[:, 1:], axis=1)  # Sum over all particle types
    
    print(f"\nInstantaneous Spectrum Analysis")
    print("=" * 50)
    print(f"Energy range: {energies[0]:.1f} to {energies[-1]:.1f} GeV")
    print(f"Peak spectrum value: {np.max(total_spectrum):.2e}")
    
    # Unit conversions
    GeV_to_erg = 1.602176634e-9  # GeV to erg
    c_light = 2.99792458e10      # cm/s
    
    # Calculate power: P = ∫ E * dN/dE dE
    energies_erg = energies * GeV_to_erg
    power_spectrum = energies_erg * total_spectrum  # erg/s per GeV
    
    # Integrate over energy
    total_power = np.trapz(power_spectrum, energies_erg)  # erg/s
    
    # Calculate mass loss rate
    mass_loss_rate_spectrum = total_power / c_light**2  # g/s
    
    print(f"Total power from spectrum: {total_power:.2e} erg/s")
    print(f"Mass loss rate from spectrum: {mass_loss_rate_spectrum:.2e} g/s")
    
    return total_power, mass_loss_rate_spectrum

def compare_methods():
    """Compare mass loss rates from evolution and spectrum"""
    
    print("Comparison of Methods")
    print("=" * 50)
    
    # Get mass loss rate from evolution (at middle time when rate is significant)
    t_mid, m_mid, dmdt_evolution = extract_mass_loss_from_evolution()

    # Find a point where mass loss rate is significant (not zero)
    valid_indices = np.where((dmdt_evolution > 0) & np.isfinite(dmdt_evolution))[0]
    if len(valid_indices) > 0:
        mid_idx = valid_indices[len(valid_indices)//2]  # Middle of valid range
        reference_dmdt = dmdt_evolution[mid_idx]
        reference_mass = m_mid[mid_idx]
        reference_time = t_mid[mid_idx]
        print(f"Using reference point: t={reference_time:.2e} s, M={reference_mass:.2e} g")
    else:
        reference_dmdt = dmdt_evolution[0]
        reference_mass = m_mid[0]
        reference_time = t_mid[0]
    
    # Get mass loss rate from spectrum
    total_power, dmdt_spectrum = analyze_instantaneous_spectrum()
    
    print(f"\nComparison at reference time:")
    print(f"Evolution method:  {reference_dmdt:.2e} g/s")
    print(f"Spectrum method:   {dmdt_spectrum:.2e} g/s")
    print(f"Ratio (evolution/spectrum): {reference_dmdt/dmdt_spectrum:.2f}")

    # The spectrum method should give the instantaneous rate
    # If there's a large discrepancy, we need to fix our spectrum calculation

    if abs(reference_dmdt/dmdt_spectrum - 1) > 0.1:  # More than 10% difference
        print(f"\n⚠️  Large discrepancy detected!")
        print(f"This suggests an error in spectrum interpretation or units.")
        
        # Let's try different interpretations
        print(f"\nTrying different spectrum interpretations:")
        
        # Maybe the spectrum units are different
        # Try without GeV conversion in the integration
        spectrum_file = "external/blackhawk_v2.3/results/test/instantaneous_primary_spectra.txt"
        data = np.loadtxt(spectrum_file, skiprows=2)
        energies = data[:, 0]  # GeV
        total_spectrum = np.sum(data[:, 1:], axis=1)

        # Unit conversions
        GeV_to_erg = 1.602176634e-9  # GeV to erg
        c_light = 2.99792458e10      # cm/s

        # Method 1: Direct integration in GeV units
        power_1 = np.trapz(energies * total_spectrum, energies) * GeV_to_erg
        dmdt_1 = power_1 / c_light**2
        print(f"Method 1 (direct GeV): {dmdt_1:.2e} g/s, ratio: {reference_dmdt/dmdt_1:.2f}")

        # Method 2: Maybe spectrum is already in erg units
        power_2 = np.trapz(energies * GeV_to_erg * total_spectrum, energies * GeV_to_erg)
        dmdt_2 = power_2 / c_light**2
        print(f"Method 2 (erg units):  {dmdt_2:.2e} g/s, ratio: {reference_dmdt/dmdt_2:.2f}")

        # Method 3: Maybe there's a missing factor
        factors_to_try = [1e-6, 1e-3, 1e3, 1e6, 1e9, 1e12]
        for factor in factors_to_try:
            dmdt_test = dmdt_spectrum * factor
            ratio = reference_dmdt / dmdt_test
            if 0.5 < ratio < 2.0:  # Within factor of 2
                print(f"Method with factor {factor:.0e}: {dmdt_test:.2e} g/s, ratio: {ratio:.2f} ✓")
    
    else:
        print(f"\n✅ Good agreement between methods!")
    
    return reference_dmdt, dmdt_spectrum

if __name__ == "__main__":
    compare_methods()
