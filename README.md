# PBH Evolution: Hawking Radiation + Superradiance

A comprehensive simulation framework for primordial black hole evolution combining Hawking radiation and superradiance effects.

## Project Overview

This project integrates two external computational tools to study the complete evolution of Kerr black holes:

- **BlackHawk v2.3**: Hawking radiation calculations
- **Isomonodromic Method**: Kerr black hole QNM calculations for superradiance

## Architecture

### Two-Phase Approach

Due to the computational complexity and interactive nature of the external tools, we employ a two-phase strategy:

#### Phase 1: Pre-computation
- Generate lookup tables for Hawking radiation rates across (M, a) parameter space
- Generate lookup tables for superradiance rates across (a/M, M*μ) parameter space
- Create interpolation-ready data formats

#### Phase 2: Evolution
- Fast interpolation from pre-computed tables
- Numerical integration of black hole parameter evolution
- Real-time evolution simulation

### Directory Structure

```
pbh-evo/
├── external/              # External computational tools
│   ├── blackhawk_v2.3/   # Hawking radiation calculator
│   └── Isomonodromic Method/  # QNM calculator
├── src/                   # Main source code
│   ├── precompute/       # Pre-computation tools
│   ├── evolution/        # Evolution engine
│   ├── interpolation/    # Fast lookup and interpolation
│   └── utils/            # Utility functions
├── data/                  # Pre-computed lookup tables
├── config/               # Configuration files
├── tests/                # Test suite
└── docs/                 # Documentation
```

## External Tools Analysis

### BlackHawk v2.3
- **Runtime**: ~3 seconds per calculation
- **Issue**: Requires interactive user input
- **Solution**: Automate parameter file generation and input responses

### Isomonodromic Method
- **Runtime**: Very long (minutes to hours per parameter point)
- **Issue**: Too slow for real-time evolution
- **Solution**: Extensive pre-computation with strategic parameter sampling

## Next Steps

1. Create automated wrappers for external tools
2. Design parameter space sampling strategies
3. Implement interpolation algorithms
4. Build evolution engine
5. Validate against known results
