"""
Configuration file for PBH evolution simulation
Defines parameter spaces for pre-computation and evolution settings
"""

import numpy as np

# =============================================================================
# PHYSICAL CONSTANTS (CGS units)
# =============================================================================

# Fundamental constants
C_LIGHT = 2.99792458e10      # cm/s
G_NEWTON = 6.67430e-8        # cm^3/g/s^2
H_PLANCK = 6.62607015e-27    # erg*s
H_BAR = H_PLANCK / (2 * np.pi)
K_BOLTZMANN = 1.380649e-16   # erg/K

# Derived constants
M_PLANCK = np.sqrt(H_BAR * C_LIGHT / G_NEWTON)  # Planck mass in g
L_PLANCK = np.sqrt(H_BAR * G_NEWTON / C_LIGHT**3)  # Planck length in cm
T_PLANCK = L_PLANCK / C_LIGHT  # Planck time in s

# Solar mass
M_SUN = 1.989e33  # g

# =============================================================================
# PARAMETER SPACE DEFINITIONS
# =============================================================================

class ParameterSpace:
    """Defines parameter spaces for pre-computation"""
    
    # BlackHawk parameter space (M, a)
    HAWKING_PARAMS = {
        # Mass range (in grams)
        'M_min': 1e9,      # Minimum mass
        'M_max': 1e16,     # Maximum mass  
        'M_points': 50,    # Number of mass points
        'M_scale': 'log',  # 'log' or 'linear'
        
        # Spin parameter range (dimensionless)
        'a_min': 0.0,      # Minimum spin
        'a_max': 0.99,     # Maximum spin (avoid extremal)
        'a_points': 30,    # Number of spin points
        'a_scale': 'linear'
    }
    
    # QNM parameter space (a/M, M*μ)
    QNM_PARAMS = {
        # Dimensionless spin a/M
        'a_over_M_min': 0.0,
        'a_over_M_max': 0.99,
        'a_over_M_points': 20,
        
        # Dimensionless mass parameter M*μ
        'M_mu_min': 0.01,
        'M_mu_max': 1.0,
        'M_mu_points': 30,
        
        # Angular quantum numbers
        'l_values': [1, 2, 3],  # Angular momentum quantum number
        'm_values': [1, 2, 3],  # Azimuthal quantum number
    }
    
    @classmethod
    def get_hawking_grid(cls):
        """Generate parameter grid for Hawking radiation pre-computation"""
        params = cls.HAWKING_PARAMS
        
        if params['M_scale'] == 'log':
            M_grid = np.logspace(np.log10(params['M_min']), 
                               np.log10(params['M_max']), 
                               params['M_points'])
        else:
            M_grid = np.linspace(params['M_min'], params['M_max'], 
                               params['M_points'])
        
        if params['a_scale'] == 'log':
            a_grid = np.logspace(np.log10(max(params['a_min'], 1e-6)), 
                               np.log10(params['a_max']), 
                               params['a_points'])
        else:
            a_grid = np.linspace(params['a_min'], params['a_max'], 
                               params['a_points'])
        
        return M_grid, a_grid
    
    @classmethod
    def get_qnm_grid(cls):
        """Generate parameter grid for QNM pre-computation"""
        params = cls.QNM_PARAMS
        
        a_over_M_grid = np.linspace(params['a_over_M_min'], 
                                   params['a_over_M_max'], 
                                   params['a_over_M_points'])
        
        M_mu_grid = np.linspace(params['M_mu_min'], 
                               params['M_mu_max'], 
                               params['M_mu_points'])
        
        return a_over_M_grid, M_mu_grid

# =============================================================================
# EVOLUTION SETTINGS
# =============================================================================

class EvolutionConfig:
    """Configuration for evolution simulation"""
    
    # Time integration settings
    TIME_INTEGRATION = {
        'method': 'RK45',           # Integration method
        'rtol': 1e-8,               # Relative tolerance
        'atol': 1e-10,              # Absolute tolerance
        'max_step': 1e-3,           # Maximum time step (in natural units)
        'min_step': 1e-12,          # Minimum time step
    }
    
    # Evolution termination conditions
    TERMINATION = {
        'min_mass_fraction': 1e-6,  # Stop when M/M_initial < this
        'max_time': 1e10,           # Maximum evolution time
        'min_mass_absolute': 10 * M_PLANCK,  # Absolute minimum mass
    }
    
    # Output settings
    OUTPUT = {
        'save_interval': 100,       # Save every N steps
        'output_format': 'hdf5',    # 'hdf5', 'csv', or 'npz'
        'precision': 'float64',     # Numerical precision
    }

# =============================================================================
# INTERPOLATION SETTINGS
# =============================================================================

class InterpolationConfig:
    """Configuration for interpolation methods"""
    
    METHODS = {
        'hawking_radiation': 'cubic',    # 'linear', 'cubic', 'quintic'
        'superradiance': 'cubic',
        'extrapolation': 'nearest',      # How to handle out-of-bounds
    }
    
    # Caching settings
    CACHE = {
        'enable': True,
        'max_size': 10000,  # Maximum number of cached interpolations
    }

# =============================================================================
# FILE PATHS
# =============================================================================

class Paths:
    """File path configurations"""
    
    # External tools
    BLACKHAWK_DIR = "external/blackhawk_v2.3"
    QNM_DIR = "external/Isomonodromic Method/Script - Julia"
    
    # Data directories
    DATA_DIR = "data"
    HAWKING_DATA = f"{DATA_DIR}/hawking_tables"
    QNM_DATA = f"{DATA_DIR}/qnm_tables"
    EVOLUTION_DATA = f"{DATA_DIR}/evolution_results"
    
    # Configuration
    CONFIG_DIR = "config"
    
    # Temporary files
    TEMP_DIR = "temp"
