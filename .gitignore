# Compiled Object files
*.o
*.obj
*.so
*.dylib
*.dll
*.a
*.lib

# Executables
*.exe
*.out
*.app
a.out

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Build directories
build/
Build/
BUILD/
bin/
Bin/
BIN/
obj/
Obj/
OBJ/

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps

# Autotools
.deps/
.libs/
*.la
*.lo
Makefile.in
aclocal.m4
autom4te.cache/
config.guess
config.sub
configure
depcomp
install-sh
ltmain.sh
missing

# Julia
*.jl.cov
*.jl.*.cov
*.jl.mem
deps/deps.jl
Manifest.toml

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Data files (common extensions)
*.dat
*.csv
*.h5
*.hdf5
*.nc
*.fits
*.txt.bak

# Results and output
results/
output/
plots/
figures/
*.png
*.jpg
*.jpeg
*.gif
*.pdf
*.eps
*.ps

# Temporary files
*.tmp
*.temp
*~
*.swp
*.swo
.DS_Store
Thumbs.db

# IDE and editor files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.atom/
.brackets.json

# Log files
*.log
*.out
*.err

# Backup files
*.bak
*.backup
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation build
docs/_build/
docs/build/
site/

# Package managers
node_modules/
.npm
.yarn/

# Local configuration
config.local.*
.env.local
.env.*.local

# Test coverage
coverage/
.coverage
.nyc_output

# Profiling
*.prof
gmon.out

# Core dumps
core
core.*

# Fortran
*.mod
*.smod

# MATLAB
*.asv
*.m~

# Mathematica
*.nb.bak
*.cdf.bak
