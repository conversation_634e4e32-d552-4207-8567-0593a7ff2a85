"""
Hawking radiation evolution module
Implements black hole mass evolution due to Hawking radiation
"""

import numpy as np
from scipy.integrate import solve_ivp
from scipy.interpolate import interp2d, RectBivariateSpline
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from config.evolution_config import ParameterSpace, EvolutionConfig
import time

class HawkingEvolution:
    """Class for evolving black holes under Hawking radiation"""
    
    def __init__(self, hawking_table_file=None):
        """
        Initialize Hawking evolution
        
        Args:
            hawking_table_file: Path to pre-computed Hawking radiation table
        """
        self.hawking_table = None
        self.mass_grid = None
        self.spin_grid = None
        self.interpolator = None
        
        if hawking_table_file and os.path.exists(hawking_table_file):
            self.load_hawking_table(hawking_table_file)
    
    def load_hawking_table(self, table_file):
        """Load pre-computed Hawking radiation table"""
        
        print(f"Loading Hawking table from {table_file}...")
        
        # Load the results array
        results = np.load(table_file, allow_pickle=True)
        
        # Extract mass and spin grids
        masses = []
        spins = []
        mass_loss_rates = []
        
        for result in results:
            masses.append(result['mass'])
            spins.append(result['spin'])
            mass_loss_rates.append(result['mass_loss_rate'])
        
        # Convert to arrays
        masses = np.array(masses)
        spins = np.array(spins)
        mass_loss_rates = np.array(mass_loss_rates)
        
        # Get unique values for grid
        unique_masses = np.unique(masses)
        unique_spins = np.unique(spins)
        
        # Create 2D grid
        mass_loss_grid = np.zeros((len(unique_masses), len(unique_spins)))
        
        for i, mass in enumerate(unique_masses):
            for j, spin in enumerate(unique_spins):
                # Find corresponding mass loss rate
                idx = np.where((masses == mass) & (spins == spin))[0]
                if len(idx) > 0:
                    mass_loss_grid[i, j] = mass_loss_rates[idx[0]]
                else:
                    # If not found, use NaN
                    mass_loss_grid[i, j] = np.nan
        
        # Store grids
        self.mass_grid = unique_masses
        self.spin_grid = unique_spins
        self.hawking_table = mass_loss_grid
        
        # Create interpolator
        # Use log scale for mass due to wide range
        log_masses = np.log10(unique_masses)
        
        # Handle any NaN values
        valid_mask = ~np.isnan(mass_loss_grid)
        if not np.all(valid_mask):
            print("Warning: Some NaN values found in Hawking table")
        
        # Create interpolator
        if len(unique_spins) == 1:
            # Special case: only one spin value, use 1D interpolation
            from scipy.interpolate import interp1d
            self.interpolator_1d = interp1d(
                log_masses, mass_loss_grid[:, 0],
                kind='cubic', bounds_error=False, fill_value='extrapolate'
            )
            self.interpolator = None
            self.single_spin = unique_spins[0]
        else:
            # General case: 2D interpolation
            self.interpolator = RectBivariateSpline(
                log_masses, unique_spins, mass_loss_grid,
                kx=min(3, len(log_masses)-1), ky=min(3, len(unique_spins)-1)
            )
            self.interpolator_1d = None
        
        print(f"Loaded table with {len(unique_masses)} mass points and {len(unique_spins)} spin points")
    
    def get_mass_loss_rate(self, mass, spin):
        """
        Get mass loss rate for given mass and spin
        
        Args:
            mass: Black hole mass in grams
            spin: Dimensionless spin parameter
            
        Returns:
            Mass loss rate in g/s
        """
        
        if self.interpolator is None and self.interpolator_1d is None:
            raise ValueError("No Hawking table loaded")

        # Ensure inputs are within bounds
        log_mass = np.log10(mass)
        log_mass_min = np.log10(self.mass_grid.min())
        log_mass_max = np.log10(self.mass_grid.max())

        # Clamp to bounds
        log_mass = np.clip(log_mass, log_mass_min, log_mass_max)

        # Interpolate
        if self.interpolator_1d is not None:
            # 1D interpolation (single spin value)
            mass_loss_rate = self.interpolator_1d(log_mass)
        else:
            # 2D interpolation
            spin_min = self.spin_grid.min()
            spin_max = self.spin_grid.max()
            spin = np.clip(spin, spin_min, spin_max)
            mass_loss_rate = self.interpolator(log_mass, spin)[0, 0]

        return mass_loss_rate
    
    def evolution_equations(self, t, y):
        """
        Evolution equations for black hole parameters
        
        Args:
            t: Time
            y: State vector [mass, spin]
            
        Returns:
            Derivatives [dmdt, dadt]
        """
        
        mass, spin = y
        
        # Get mass loss rate from Hawking radiation
        dmdt = -self.get_mass_loss_rate(mass, spin)
        
        # For now, assume spin doesn't change (Schwarzschild case)
        # In reality, spin evolution is more complex
        dadt = 0.0
        
        return [dmdt, dadt]
    
    def evolve_black_hole(self, 
                         initial_mass, 
                         initial_spin=0.0,
                         final_mass_fraction=1e-6,
                         max_time=1e10,
                         rtol=1e-8,
                         atol=1e-10):
        """
        Evolve a black hole under Hawking radiation
        
        Args:
            initial_mass: Initial mass in grams
            initial_spin: Initial spin parameter
            final_mass_fraction: Stop when M/M_initial < this
            max_time: Maximum evolution time in seconds
            rtol: Relative tolerance for integrator
            atol: Absolute tolerance for integrator
            
        Returns:
            Dictionary with evolution results
        """
        
        print(f"Evolving black hole: M_i = {initial_mass:.2e} g, a_i = {initial_spin:.3f}")
        
        # Initial conditions
        y0 = [initial_mass, initial_spin]
        
        # Final mass
        final_mass = initial_mass * final_mass_fraction
        
        # Event function to stop when mass gets too small
        def mass_event(t, y):
            return y[0] - final_mass
        
        mass_event.terminal = True
        mass_event.direction = -1
        
        # Time span
        t_span = (0, max_time)
        
        # Solve ODE
        start_time = time.time()
        
        try:
            sol = solve_ivp(
                self.evolution_equations,
                t_span,
                y0,
                events=mass_event,
                rtol=rtol,
                atol=atol,
                method='DOP853',  # High-order method for stiff problems
                dense_output=True
            )
            
            computation_time = time.time() - start_time
            
            if not sol.success:
                raise RuntimeError(f"Integration failed: {sol.message}")
            
            # Extract results
            times = sol.t
            masses = sol.y[0]
            spins = sol.y[1]
            
            # Calculate final lifetime
            if len(sol.t_events[0]) > 0:
                lifetime = sol.t_events[0][0]
                terminated_by_mass = True
            else:
                lifetime = times[-1]
                terminated_by_mass = False
            
            print(f"Evolution completed in {computation_time:.2f} seconds")
            print(f"Black hole lifetime: {lifetime:.2e} seconds")
            print(f"Final mass: {masses[-1]:.2e} g ({masses[-1]/initial_mass:.2e} of initial)")
            print(f"Terminated by mass limit: {terminated_by_mass}")
            
            return {
                'times': times,
                'masses': masses,
                'spins': spins,
                'lifetime': lifetime,
                'initial_mass': initial_mass,
                'final_mass': masses[-1],
                'computation_time': computation_time,
                'terminated_by_mass': terminated_by_mass,
                'integrator_info': {
                    'nfev': sol.nfev,
                    'njev': sol.njev,
                    'nlu': sol.nlu
                }
            }
            
        except Exception as e:
            print(f"Evolution failed: {e}")
            raise

def schwarzschild_lifetime_analytical(mass):
    """
    Calculate analytical Schwarzschild black hole lifetime
    
    Args:
        mass: Mass in grams
        
    Returns:
        Lifetime in seconds
    """
    
    # Physical constants (CGS)
    G = 6.67430e-8      # cm^3/g/s^2
    c = 2.99792458e10   # cm/s
    hbar = 1.054571817e-27  # erg*s
    
    # Schwarzschild lifetime formula
    # t = (5120 * π * G^2 * M^3) / (hbar * c^4)
    
    coefficient = 5120 * np.pi * G**2 / (hbar * c**4)
    lifetime = coefficient * mass**3
    
    return lifetime
