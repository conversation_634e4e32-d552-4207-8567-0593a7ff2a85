#!/usr/bin/env python3
"""
Test script for 1kg Schwarzschild black hole evolution
Validates against known analytical lifetime of ~2e-19 seconds
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

import numpy as np
from src.precompute.blackhawk_wrapper import BlackHawkWrapper
from src.evolution.hawking_evolution import HawkingEvolution, schwarzschild_lifetime_analytical
from config.evolution_config import Paths, ParameterSpace
import time

def create_hawking_table():
    """Create Hawking radiation table from Planck mass to 1kg"""
    
    print("Creating Hawking radiation table...")
    
    # Physical constants
    M_PLANCK = 2.176434e-5  # Planck mass in grams
    
    # Mass range: Planck mass to 1 kg
    M_min = M_PLANCK
    M_max = 1000.0  # 1 kg in grams
    n_mass_points = 100  # Dense sampling for accuracy
    
    # Only Schwarzschild (spin = 0)
    spin_values = [0.0]
    
    # Create logarithmic mass grid
    mass_grid = np.logspace(np.log10(M_min), np.log10(M_max), n_mass_points)
    spin_grid = np.array(spin_values)
    
    print(f"Mass range: {M_min:.2e} to {M_max:.2e} g")
    print(f"Number of mass points: {n_mass_points}")
    print(f"Spin values: {spin_values}")
    
    # Initialize BlackHawk wrapper
    wrapper = BlackHawkWrapper(Paths.BLACKHAWK_DIR)
    
    # Progress callback
    def progress_callback(progress, mass, spin, result):
        print(f"Progress: {progress*100:.1f}% - M={mass:.2e} g, "
              f"dM/dt={result['mass_loss_rate']:.2e} g/s")
    
    # Compute table
    output_file = "data/hawking_table_1kg_test.npy"
    
    # Create data directory if it doesn't exist
    os.makedirs("data", exist_ok=True)
    
    start_time = time.time()
    wrapper.batch_compute(mass_grid, spin_grid, output_file, progress_callback)
    computation_time = time.time() - start_time
    
    print(f"\nTable computation completed in {computation_time:.1f} seconds")
    print(f"Table saved to: {output_file}")
    
    return output_file

def test_1kg_evolution():
    """Test evolution of 1kg Schwarzschild black hole"""
    
    print("\n" + "="*60)
    print("Testing 1kg Schwarzschild Black Hole Evolution")
    print("="*60)
    
    # Create or load Hawking table
    table_file = "data/hawking_table_1kg_test.npy"
    
    if not os.path.exists(table_file):
        print("Hawking table not found. Creating new table...")
        table_file = create_hawking_table()
    else:
        print(f"Using existing Hawking table: {table_file}")
    
    # Initialize evolution
    evolution = HawkingEvolution(table_file)
    
    # Test parameters
    initial_mass = 1000.0  # 1 kg in grams
    initial_spin = 0.0     # Schwarzschild
    
    # Calculate analytical lifetime
    analytical_lifetime = schwarzschild_lifetime_analytical(initial_mass)
    print(f"\nAnalytical Schwarzschild lifetime: {analytical_lifetime:.2e} seconds")
    
    # Evolve black hole
    print(f"\nStarting numerical evolution...")
    
    try:
        result = evolution.evolve_black_hole(
            initial_mass=initial_mass,
            initial_spin=initial_spin,
            final_mass_fraction=1e-6,  # Stop at 0.0001% of initial mass
            max_time=analytical_lifetime * 10,  # Safety factor
            rtol=1e-10,  # High precision
            atol=1e-12
        )
        
        # Compare results
        numerical_lifetime = result['lifetime']
        relative_error = abs(numerical_lifetime - analytical_lifetime) / analytical_lifetime
        
        print(f"\n" + "="*60)
        print("RESULTS COMPARISON")
        print("="*60)
        print(f"Analytical lifetime:  {analytical_lifetime:.6e} seconds")
        print(f"Numerical lifetime:   {numerical_lifetime:.6e} seconds")
        print(f"Relative error:       {relative_error:.2%}")
        print(f"Integration steps:    {len(result['times'])}")
        print(f"Function evaluations: {result['integrator_info']['nfev']}")
        print(f"Computation time:     {result['computation_time']:.2f} seconds")
        
        # Validation
        if relative_error < 0.1:  # 10% tolerance
            print(f"\n✓ VALIDATION PASSED: Error {relative_error:.2%} < 10%")
            return True
        else:
            print(f"\n✗ VALIDATION FAILED: Error {relative_error:.2%} > 10%")
            return False
            
    except Exception as e:
        print(f"\n✗ EVOLUTION FAILED: {e}")
        return False

def test_mass_loss_rate_scaling():
    """Test that mass loss rate scales correctly with mass"""
    
    print("\n" + "="*60)
    print("Testing Mass Loss Rate Scaling")
    print("="*60)
    
    table_file = "data/hawking_table_1kg_test.npy"
    
    if not os.path.exists(table_file):
        print("Creating Hawking table for scaling test...")
        table_file = create_hawking_table()
    
    evolution = HawkingEvolution(table_file)
    
    # Test different masses
    test_masses = np.array([1e-3, 1e-2, 1e-1, 1.0, 10.0, 100.0, 1000.0])  # grams
    
    print("Mass (g)        dM/dt (g/s)     Scaling")
    print("-" * 45)
    
    mass_loss_rates = []
    for mass in test_masses:
        try:
            rate = evolution.get_mass_loss_rate(mass, 0.0)
            mass_loss_rates.append(rate)
            
            # Expected scaling: dM/dt ∝ 1/M^2
            if len(mass_loss_rates) > 1:
                expected_ratio = (test_masses[0] / mass)**2
                actual_ratio = mass_loss_rates[0] / rate
                scaling_error = abs(actual_ratio - expected_ratio) / expected_ratio
                print(f"{mass:8.1e}     {rate:8.2e}     {scaling_error:6.1%}")
            else:
                print(f"{mass:8.1e}     {rate:8.2e}     reference")
                
        except Exception as e:
            print(f"{mass:8.1e}     ERROR: {e}")
    
    return True

if __name__ == "__main__":
    print("1kg Schwarzschild Black Hole Test")
    print("=" * 60)
    
    # Test mass loss rate scaling
    test_mass_loss_rate_scaling()
    
    # Test full evolution
    success = test_1kg_evolution()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        sys.exit(1)
