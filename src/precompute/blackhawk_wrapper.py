"""
Automated wrapper for BlackHawk v2.3
Handles parameter file generation and automated execution
"""

import os
import subprocess
import tempfile
import shutil
from pathlib import Path
import numpy as np
from typing import Tuple, Dict, Optional
import time

class BlackHawkWrapper:
    """Wrapper class for automated BlackHawk execution"""
    
    def __init__(self, blackhawk_dir: str):
        """
        Initialize BlackHawk wrapper

        Args:
            blackhawk_dir: Path to BlackHawk installation directory
        """
        self.blackhawk_dir = Path(blackhawk_dir).resolve()
        self.executable = self.blackhawk_dir / "BlackHawk_inst.x"

        # Check if BlackHawk is compiled
        if not self.executable.exists():
            raise FileNotFoundError(f"BlackHawk executable not found at {self.executable}")
    
    def create_parameter_file(self, 
                            mass: float, 
                            spin: float,
                            output_dir: str = "temp_output",
                            primary_only: bool = True) -> str:
        """
        Create a parameter file for BlackHawk
        
        Args:
            mass: Black hole mass in grams
            spin: Dimensionless spin parameter (0 <= a < 1)
            output_dir: Output directory name
            primary_only: If True, compute only primary spectra (faster)
            
        Returns:
            Path to created parameter file
        """
        
        # Create temporary parameter file
        param_content = f"""destination_folder = {output_dir}
full_output = 0
interpolation_method = 0

metric = 0

BH_number = 1
Mmin = {mass:.6e}
Mmax = {mass:.6e}
param_number = 1
amin = {spin:.6f}
amax = {spin:.6f}
Qmin = 0.
Qmax = 0.

epsilon_LQG = 1.5
a0_LQG = 0.
n = 0.

spectrum_choice = 0
spectrum_choice_param = 0

amplitude_lognormal = 1.
amplitude_lognormal2 = 1.
stand_dev_lognormal = 1.
crit_mass_lognormal = 1.

amplitude_powerlaw = 1.
eqstate_powerlaw = 0.3333

amplitude_critical_collapse = 1.
crit_mass_critical_collapse = 1.

amplitude_uniform = 1.

stand_dev_param_Gaussian = 1.
mean_param_Gaussian = 0.5

table = spin_distribution_BH.txt

tmin_manual = 0
tmin = 1.e-30
limit = 5000
BH_remnant = 0
M_remnant = 1e-4

E_number = 10
Emin = 5
Emax = 1e+5

grav = 1
add_DM = 0
m_DM = 1.
spin_DM = 0.
dof_DM = 1.

primary_only = {1 if primary_only else 0}

hadronization_choice = 2
"""
        
        # Write to temporary file
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
        temp_file.write(param_content)
        temp_file.close()
        
        return temp_file.name
    
    def run_blackhawk(self, 
                     mass: float, 
                     spin: float,
                     timeout: float = 60.0) -> Dict:
        """
        Run BlackHawk calculation for given parameters
        
        Args:
            mass: Black hole mass in grams
            spin: Dimensionless spin parameter
            timeout: Maximum execution time in seconds
            
        Returns:
            Dictionary containing results and metadata
        """
        
        # Create unique output directory
        output_dir = f"bh_M{mass:.2e}_a{spin:.3f}_{int(time.time())}"
        
        # Create parameter file
        param_file = self.create_parameter_file(mass, spin, output_dir)
        
        try:
            # Change to BlackHawk directory
            original_dir = os.getcwd()
            os.chdir(self.blackhawk_dir)
            
            # Run BlackHawk with automatic input handling
            start_time = time.time()

            # Use Popen for better input control
            process = subprocess.Popen(
                [str(self.executable), param_file],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Send automatic responses
            stdout, stderr = process.communicate(input="y\ny\n", timeout=timeout)
            execution_time = time.time() - start_time

            # Create result object
            result = type('Result', (), {
                'returncode': process.returncode,
                'stdout': stdout,
                'stderr': stderr
            })()
            
            # Check if execution was successful (BlackHawk may return 1 even on success)
            if result.returncode != 0 and "END OF EXECUTION" not in result.stdout:
                raise RuntimeError(f"BlackHawk execution failed (code {result.returncode}):\n"
                                 f"STDOUT: {result.stdout}\n"
                                 f"STDERR: {result.stderr}")
            
            # Parse results
            results = self._parse_blackhawk_output(output_dir)
            results['execution_time'] = execution_time
            results['mass'] = mass
            results['spin'] = spin
            
            # Clean up output directory
            output_path = self.blackhawk_dir / "results" / output_dir
            if output_path.exists():
                shutil.rmtree(output_path)
            
            return results
            
        finally:
            # Clean up
            os.chdir(original_dir)
            if os.path.exists(param_file):
                os.unlink(param_file)
    
    def _parse_blackhawk_output(self, output_dir: str) -> Dict:
        """
        Parse BlackHawk output files
        
        Args:
            output_dir: Output directory name
            
        Returns:
            Dictionary containing parsed results
        """
        
        results_path = self.blackhawk_dir / "results" / output_dir
        spectrum_file = results_path / "instantaneous_primary_spectra.txt"
        
        if not spectrum_file.exists():
            raise FileNotFoundError(f"Output file not found: {spectrum_file}")
        
        # Read spectrum data
        try:
            # Skip header lines and read data
            data = np.loadtxt(spectrum_file, skiprows=2)
            
            if data.size == 0:
                raise ValueError("No data found in spectrum file")
            
            # Extract energy and particle spectra
            energies = data[:, 0]  # GeV
            
            # Column indices for different particles (from header analysis)
            particle_columns = {
                'photon': 1,
                'gluons': 2,
                'higgs': 3,
                'W_boson': 4,
                'Z_boson': 5,
                'neutrinos': 6,
                'electron': 7,
                'muon': 8,
                'tau': 9,
                'up': 10,
                'down': 11,
                'charm': 12,
                'strange': 13,
                'top': 14,
                'bottom': 15,
                'graviton': 16,
                'DM': 17
            }
            
            spectra = {}
            for particle, col_idx in particle_columns.items():
                if col_idx < data.shape[1]:
                    spectra[particle] = data[:, col_idx]
            
            # Calculate total power (integrate over energy)
            total_power = self._calculate_total_power(energies, spectra)
            
            return {
                'energies': energies,
                'spectra': spectra,
                'total_power': total_power,
                'mass_loss_rate': self._calculate_mass_loss_rate(total_power)
            }
            
        except Exception as e:
            raise RuntimeError(f"Failed to parse BlackHawk output: {e}")
    
    def _calculate_total_power(self, energies: np.ndarray, spectra: Dict) -> float:
        """Calculate total radiated power"""
        
        # Convert GeV to erg
        GeV_to_erg = 1.602176634e-9  # GeV to erg conversion (1 GeV = 1.602e-9 erg)
        
        total_power = 0.0
        for particle, spectrum in spectra.items():
            if len(spectrum) > 0:
                # Integrate E * dN/dE over energy
                power_spectrum = energies * spectrum * GeV_to_erg
                total_power += np.trapz(power_spectrum, energies * GeV_to_erg)
        
        return total_power  # erg/s
    
    def _calculate_mass_loss_rate(self, total_power: float) -> float:
        """Calculate mass loss rate from total power"""
        
        c_light = 2.99792458e10  # cm/s
        return total_power / c_light**2  # g/s
    
    def batch_compute(self, 
                     mass_grid: np.ndarray, 
                     spin_grid: np.ndarray,
                     output_file: str,
                     progress_callback: Optional[callable] = None) -> None:
        """
        Compute Hawking radiation for a grid of parameters
        
        Args:
            mass_grid: Array of masses in grams
            spin_grid: Array of spin parameters
            output_file: Path to save results
            progress_callback: Optional callback for progress updates
        """
        
        total_points = len(mass_grid) * len(spin_grid)
        results = []
        
        for i, mass in enumerate(mass_grid):
            for j, spin in enumerate(spin_grid):
                try:
                    result = self.run_blackhawk(mass, spin)
                    results.append(result)
                    
                    if progress_callback:
                        progress = (i * len(spin_grid) + j + 1) / total_points
                        progress_callback(progress, mass, spin, result)
                        
                except Exception as e:
                    print(f"Error computing M={mass:.2e}, a={spin:.3f}: {e}")
                    continue
        
        # Save results
        np.save(output_file, results)
