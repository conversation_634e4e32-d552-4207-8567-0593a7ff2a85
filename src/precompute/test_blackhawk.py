#!/usr/bin/env python3
"""
Test script for BlackHawk wrapper
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.precompute.blackhawk_wrapper import BlackHawkWrapper
from config.evolution_config import Paths
import numpy as np

def test_single_calculation():
    """Test a single BlackHawk calculation"""
    
    print("Testing BlackHawk wrapper...")
    
    # Initialize wrapper
    wrapper = BlackHawkWrapper(Paths.BLACKHAWK_DIR)
    
    # Test parameters
    mass = 1e12  # grams
    spin = 0.5   # dimensionless
    
    print(f"Computing Hawking radiation for M={mass:.2e} g, a={spin}")
    
    try:
        result = wrapper.run_blackhawk(mass, spin)
        
        print("Success!")
        print(f"Execution time: {result['execution_time']:.2f} seconds")
        print(f"Total power: {result['total_power']:.2e} erg/s")
        print(f"Mass loss rate: {result['mass_loss_rate']:.2e} g/s")
        print(f"Number of energy points: {len(result['energies'])}")
        print(f"Available particles: {list(result['spectra'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_small_grid():
    """Test computation on a small parameter grid"""
    
    print("\nTesting small parameter grid...")
    
    wrapper = BlackHawkWrapper(Paths.BLACKHAWK_DIR)
    
    # Small test grid
    mass_grid = np.array([1e11, 1e12, 1e13])  # grams
    spin_grid = np.array([0.0, 0.5, 0.9])     # dimensionless
    
    def progress_callback(progress, mass, spin, result):
        print(f"Progress: {progress*100:.1f}% - M={mass:.1e}, a={spin:.1f}, "
              f"P={result['total_power']:.2e} erg/s")
    
    try:
        output_file = "test_hawking_grid.npy"
        wrapper.batch_compute(mass_grid, spin_grid, output_file, progress_callback)
        
        # Load and verify results
        results = np.load(output_file, allow_pickle=True)
        print(f"\nComputed {len(results)} parameter points")
        
        # Clean up
        if os.path.exists(output_file):
            os.remove(output_file)
            
        return True
        
    except Exception as e:
        print(f"Error in grid computation: {e}")
        return False

if __name__ == "__main__":
    print("BlackHawk Wrapper Test")
    print("=" * 50)
    
    # Test single calculation
    success1 = test_single_calculation()
    
    # Test grid computation
    success2 = test_small_grid()
    
    if success1 and success2:
        print("\n✓ All tests passed!")
    else:
        print("\n✗ Some tests failed!")
        sys.exit(1)
