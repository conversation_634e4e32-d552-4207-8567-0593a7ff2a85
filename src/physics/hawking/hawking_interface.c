// BlackHawk integration interface for Hawking radiation calculations
// Provides a clean interface to BlackHawk v2.3 functionality

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "../../include/hawking_interface.h"
#include "../../include/improved_structures.h"

// Physical constants
#define PI 3.14159265358979323846
#define G_NEWTON 6.67430e-11     // m^3 kg^-1 s^-2
#define C_LIGHT 2.99792458e8     // m/s
#define HBAR 1.054571817e-34     // J⋅s
#define K_BOLTZMANN 1.380649e-23 // J/K
#define M_PLANCK 2.176434e-8     // kg

// Global BlackHawk configuration
static BlackHawkConfig global_config = {0};

// Initialize BlackHawk interface
EvolutionError initialize_blackhawk(BlackHawkConfig* config, const char* param_file) {
    if (!config) return EVOLUTION_ERROR_CONFIG;

    // Set default parameters
    config->initialized = 0;
    config->blackhawk_data = NULL;

    // Particle types (enable all by default)
    config->include_photons = 1;
    config->include_neutrinos = 1;
    config->include_electrons = 1;
    config->include_muons = 1;
    config->include_pions = 1;
    config->include_quarks = 1;

    // Energy range
    config->energy_min = 1e-6;   // GeV
    config->energy_max = 1e6;    // GeV
    config->energy_bins = 1000;

    // Spin effects
    config->include_spin_effects = 1;
    config->spin_precision = 1e-6;

    if (param_file) {
        strncpy(config->param_file, param_file, sizeof(config->param_file) - 1);
        config->param_file[sizeof(config->param_file) - 1] = '\0';
    } else {
        strcpy(config->param_file, "./external/blackhawk_v2.3/parameters.txt");
    }

    // TODO: Initialize actual BlackHawk library
    // For now, mark as initialized for placeholder calculations
    config->initialized = 1;
    global_config = *config;

    return EVOLUTION_SUCCESS;
}

// Calculate Hawking temperature
double calculate_hawking_temperature(double M, double a) {
    if (M <= 0) return 0.0;

    // Hawking temperature for Kerr black hole
    // T_H = (hbar * c^3) / (8 * pi * G * M * k_B) * (1 + sqrt(1 - a^2)) / 2
    double a_norm = fabs(a);
    if (a_norm >= 1.0) a_norm = 0.999; // Avoid extremal case

    double factor = (1.0 + sqrt(1.0 - a_norm * a_norm)) / 2.0;
    double T_H = (HBAR * C_LIGHT * C_LIGHT * C_LIGHT) /
                 (8.0 * PI * G_NEWTON * M * K_BOLTZMANN) * factor;

    return T_H;
}

// Calculate black hole entropy
double calculate_black_hole_entropy(double M, double a) {
    if (M <= 0) return 0.0;

    // Bekenstein-Hawking entropy for Kerr black hole
    // S = (k_B * c^3 * A) / (4 * G * hbar)
    // where A is the area of the event horizon

    double a_norm = fabs(a);
    if (a_norm >= 1.0) a_norm = 0.999;

    double r_plus = G_NEWTON * M / (C_LIGHT * C_LIGHT) *
                    (1.0 + sqrt(1.0 - a_norm * a_norm));
    double area = 4.0 * PI * (r_plus * r_plus +
                             (G_NEWTON * M * a_norm / C_LIGHT) *
                             (G_NEWTON * M * a_norm / C_LIGHT));

    double entropy = (K_BOLTZMANN * C_LIGHT * C_LIGHT * C_LIGHT * area) /
                     (4.0 * G_NEWTON * HBAR);

    return entropy;
}

// Estimate evaporation time
double estimate_evaporation_time(double M, double a) {
    if (M <= 0) return 0.0;

    // Approximate evaporation time for Kerr black hole
    // t_evap ~ (M/M_Planck)^3 * t_Planck
    double M_ratio = M / M_PLANCK;
    double t_planck = HBAR / (M_PLANCK * C_LIGHT * C_LIGHT);

    // Spin correction factor (approximate)
    double a_norm = fabs(a);
    if (a_norm >= 1.0) a_norm = 0.999;
    double spin_factor = 1.0 + 0.5 * a_norm * a_norm; // Rough approximation

    return M_ratio * M_ratio * M_ratio * t_planck / spin_factor;
}