#ifndef HAWKING_INTERFACE_H
#define HAWKING_INTERFACE_H

#include "evolution.h"

// BlackHawk parameter structure
typedef struct {
    char param_file[256];    // BlackHawk parameter file
    int initialized;         // Initialization flag
    void* blackhawk_data;    // Internal BlackHawk data

    // Particle types to include
    int include_photons;
    int include_neutrinos;
    int include_electrons;
    int include_muons;
    int include_pions;
    int include_quarks;

    // Calculation parameters
    double energy_min;       // Minimum energy (GeV)
    double energy_max;       // Maximum energy (GeV)
    int energy_bins;         // Number of energy bins

    // Spin-dependent parameters
    int include_spin_effects; // Include spin-dependent emission
    double spin_precision;   // Precision for spin calculations

} BlackHawkConfig;

// Hawking radiation results
typedef struct {
    double mass_rate;        // dM/dt (kg/s)
    double spin_rate;        // da/dt (1/s)
    double power_total;      // Total power (W)
    double temperature;      // Hawking temperature (K)
    double entropy;          // Black hole entropy

    // Particle-specific rates
    double photon_rate;
    double neutrino_rate;
    double electron_rate;
    double muon_rate;

    // Spectral information
    double* energy_spectrum; // Energy spectrum
    double* particle_spectrum; // Particle spectrum
    int spectrum_size;       // Spectrum array size

} HawkingResult;

// Function declarations
EvolutionError initialize_blackhawk(BlackHawkConfig* config, const char* param_file);
void cleanup_blackhawk(BlackHawkConfig* config);

EvolutionError calculate_hawking_rates(const BlackHoleState* bh,
                                     BlackHawkConfig* config,
                                     HawkingResult* result);

EvolutionError calculate_hawking_spectrum(const BlackHoleState* bh,
                                        BlackHawkConfig* config,
                                        HawkingResult* result);

// Utility functions
double calculate_hawking_temperature(double M, double a);
double calculate_black_hole_entropy(double M, double a);
double estimate_evaporation_time(double M, double a);

// Validation functions
EvolutionError validate_hawking_result(const HawkingResult* result);
int check_hawking_consistency(const BlackHoleState* bh, const HawkingResult* result);

#endif // HAWKING_INTERFACE_H