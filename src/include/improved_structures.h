#ifndef IMPROVED_STRUCTURES_H
#define IMPROVED_STRUCTURES_H

#include <stdio.h>
#include <time.h>

// Forward declarations
typedef struct EvolutionConfig EvolutionConfig;
typedef struct BlackHoleState BlackHoleState;
typedef struct Logger Logger;

// Error handling with context
typedef enum {
    EVOLUTION_SUCCESS = 0,
    EVOLUTION_ERROR_CONVERGENCE,
    EVOLUTION_ERROR_TIMESTEP,
    EVOLUTION_ERROR_QNM,
    EVOLUTION_ERROR_IO,
    EVOLUTION_ERROR_CONFIG,
    EVOLUTION_ERROR_MEMORY,
    EVOLUTION_ERROR_PHYSICS,
    EVOLUTION_ERROR_HAWKING,
    EVOLUTION_ERROR_JULIA,
    EVOLUTION_ERROR_VALIDATION
} EvolutionError;

// Error context for debugging
typedef struct {
    EvolutionError code;
    char message[256];
    char function[64];
    char file[64];
    int line;
    time_t timestamp;
} ErrorContext;

// Logging levels
typedef enum {
    LOG_DEBUG = 0,
    LOG_INFO,
    LOG_WARNING,
    LOG_ERROR,
    LOG_CRITICAL
} LogLevel;

// Logger structure
typedef struct Logger {
    FILE* file;
    LogLevel min_level;
    int console_output;
    char log_file[256];
    int initialized;
} Logger;

// Performance metrics
typedef struct {
    double total_cpu_time;
    double qnm_calculation_time;
    double hawking_calculation_time;
    double integration_time;
    int qnm_cache_hits;
    int qnm_cache_misses;
    int total_evolution_steps;
    double average_timestep;
} PerformanceMetrics;

#endif // IMPROVED_STRUCTURES_H