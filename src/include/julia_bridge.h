#ifndef JULIA_BRIDGE_H
#define JULIA_BRIDGE_H

#include "qnm_interface.h"

// Julia session management
typedef struct {
    void* julia_session;     // Julia session handle
    int initialized;         // Initialization flag
    int persistent;          // Keep session alive
    char julia_script_path[256]; // Path to Julia scripts
} JuliaBridge;

// Improved QNM cache with hash table
typedef struct QNMCacheEntry {
    double a_over_M;         // Normalized spin
    double mu_times_M;       // Normalized mass
    int l, m, n;             // Quantum numbers
    QNMResult result;        // Cached result
    time_t timestamp;        // Cache timestamp
    int access_count;        // Access frequency
    struct QNMCacheEntry* next; // Hash table chain
} QNMCacheEntry;

typedef struct {
    QNMCacheEntry** table;   // Hash table
    int size;                // Table size
    int count;               // Number of entries
    int max_size;            // Maximum entries
    int hits;                // Cache hits
    int misses;              // Cache misses
} QNMCache;

// Function declarations
EvolutionError initialize_julia_bridge(<PERSON><PERSON><PERSON>* bridge, const char* script_path);
void cleanup_julia_bridge(Julia<PERSON><PERSON>* bridge);

EvolutionError julia_calculate_qnm(Julia<PERSON>ridge* bridge,
                                  double M, double a, double mu,
                                  int l, int m, int n,
                                  QNMResult* result);

// Enhanced cache management
EvolutionError initialize_qnm_cache(QNMCache* cache, int max_size);
void cleanup_qnm_cache(QNMCache* cache);
QNMResult* cache_lookup(QNMCache* cache, double a_M, double mu_M, int l, int m, int n);
void cache_insert(QNMCache* cache, double a_M, double mu_M, int l, int m, int n,
                  const QNMResult* result);
void cache_statistics(const QNMCache* cache, int* hits, int* misses, double* hit_rate);

// Cache persistence
EvolutionError save_cache_to_file(const QNMCache* cache, const char* filename);
EvolutionError load_cache_from_file(QNMCache* cache, const char* filename);

#endif // JULIA_BRIDGE_H