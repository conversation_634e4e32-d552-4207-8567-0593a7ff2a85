// Comprehensive logging system for pbh-evo
// Provides structured logging with multiple levels and output targets

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <time.h>
#include <sys/stat.h>
#include "../include/improved_structures.h"

// Global logger instance
static Logger global_logger = {0};

// Log level names
static const char* log_level_names[] = {
    "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"
};

// Initialize logger
EvolutionError initialize_logger(Logger* logger, const char* log_file, LogLevel min_level) {
    if (!logger) return EVOLUTION_ERROR_CONFIG;

    // Set parameters
    logger->min_level = min_level;
    logger->console_output = 1;
    logger->initialized = 0;

    if (log_file && strlen(log_file) > 0) {
        strncpy(logger->log_file, log_file, sizeof(logger->log_file) - 1);
        logger->log_file[sizeof(logger->log_file) - 1] = '\0';

        // Create directory if needed
        char dir_path[256];
        strncpy(dir_path, log_file, sizeof(dir_path) - 1);
        char* last_slash = strrchr(dir_path, '/');
        if (last_slash) {
            *last_slash = '\0';
            struct stat st = {0};
            if (stat(dir_path, &st) == -1) {
                if (mkdir(dir_path, 0755) != 0) {
                    return EVOLUTION_ERROR_IO;
                }
            }
        }

        // Open log file
        logger->file = fopen(log_file, "a");
        if (!logger->file) {
            return EVOLUTION_ERROR_IO;
        }

        // Write header
        time_t now = time(NULL);
        fprintf(logger->file, "\n=== PBH-EVO Log Session Started: %s", ctime(&now));
        fflush(logger->file);
    } else {
        logger->file = NULL;
    }

    logger->initialized = 1;
    global_logger = *logger;

    return EVOLUTION_SUCCESS;
}

// Log a message with specified level
void log_message(Logger* logger, LogLevel level, const char* format, ...) {
    if (!logger || !logger->initialized || level < logger->min_level) {
        return;
    }

    // Get current time
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    char timestamp[64];
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);

    // Format message
    va_list args;
    va_start(args, format);
    char message[1024];
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);

    // Create log entry
    char log_entry[1200];
    snprintf(log_entry, sizeof(log_entry), "[%s] %s: %s\n",
             timestamp, log_level_names[level], message);

    // Output to console if enabled
    if (logger->console_output && level >= LOG_INFO) {
        printf("%s", log_entry);
        fflush(stdout);
    }

    // Output to file if available
    if (logger->file) {
        fprintf(logger->file, "%s", log_entry);
        fflush(logger->file);
    }
}

// Log error with context
void log_error(const ErrorContext* ctx) {
    if (!ctx) return;

    char error_msg[512];
    snprintf(error_msg, sizeof(error_msg),
             "Error in %s() at %s:%d - %s",
             ctx->function, ctx->file, ctx->line, ctx->message);

    log_message(&global_logger, LOG_ERROR, "%s", error_msg);
}

// Cleanup logger
void cleanup_logger(Logger* logger) {
    if (!logger || !logger->initialized) return;

    if (logger->file) {
        time_t now = time(NULL);
        fprintf(logger->file, "=== Log Session Ended: %s\n", ctime(&now));
        fclose(logger->file);
        logger->file = NULL;
    }

    logger->initialized = 0;
}